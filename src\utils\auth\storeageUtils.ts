// Storage keys
const STORAGE_KEYS = {
    TOKEN: 'token',
    REFRESH_TOKEN: 'refresh_token',
    ROLES: 'user_roles',
    ORGANIZATION_CODE: 'organization_code',
    <PERSON><PERSON><PERSON>_CODE: 'branch_code',
} as const

export const storeAuthData = (
    token: string,
    refreshToken: string,
    roles: string[],
    organizationCode?: string,
): void => {
    localStorage.setItem(STORAGE_KEYS.TOKEN, token)
    localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, refreshToken)
    localStorage.setItem(STORAGE_KEYS.ROLES, JSON.stringify(roles))
    if (organizationCode) {
        localStorage.setItem(STORAGE_KEYS.ORGANIZATION_CODE, organizationCode)
    }
}

export const getAuthData = () => {
    try {
        return {
            token: localStorage.getItem(STORAGE_KEYS.TOKEN),
            refreshToken: localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN),
            roles: JSON.parse(localStorage.getItem(STORAGE_KEYS.ROLES) || '[]'),
            organizationCode: localStorage.getItem(
                STORAGE_KEYS.ORGANIZATION_CODE,
            ),
            branchCode: localStorage.getItem(STORAGE_KEYS.BRANCH_CODE),
        }
    } catch (error) {
        console.error('Error getting auth data:', error)
        return {
            token: null,
            refreshToken: null,
            roles: [],
            organizationCode: null,
            branchCode: null,
        }
    }
}

export const storeBranchCode = (branchCode: string): void => {
    localStorage.setItem(STORAGE_KEYS.BRANCH_CODE, branchCode)
}

export const clearAuthData = (): void => {
    localStorage.removeItem(STORAGE_KEYS.TOKEN)
    localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN)
    localStorage.removeItem(STORAGE_KEYS.ROLES)
    localStorage.removeItem(STORAGE_KEYS.ORGANIZATION_CODE)
    localStorage.removeItem(STORAGE_KEYS.BRANCH_CODE)
}
