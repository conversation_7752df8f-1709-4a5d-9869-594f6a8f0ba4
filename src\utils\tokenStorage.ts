import cookiesStorage from '@/utils/cookiesStorage'
import appConfig from '@/configs/app.config'
import { TOKEN_NAME_IN_STORAGE, REFRESH_TOKEN_NAME_IN_STORAGE } from '@/constants/api.constant'

const getPersistStorage = () => {
    if (appConfig.accessTokenPersistStrategy === 'localStorage') {
        return localStorage
    }

    if (appConfig.accessTokenPersistStrategy === 'sessionStorage') {
        return sessionStorage
    }

    return cookiesStorage
}

export const tokenStorage = {
    getToken: (): string | null | Promise<string | null> => {
        const storage = getPersistStorage()
        return storage.getItem(TOKEN_NAME_IN_STORAGE)
    },

    setToken: (token: string): void => {
        const storage = getPersistStorage()
        storage.setItem(TOKEN_NAME_IN_STORAGE, token)
    },

    removeToken: (): void => {
        const storage = getPersistStorage()
        storage.removeItem(TOKEN_NAME_IN_STORAGE)
    },

    getRefreshToken: (): string | null | Promise<string | null> => {
        const storage = getPersistStorage()
        return storage.getItem(REFRESH_TOKEN_NAME_IN_STORAGE)
    },

    setRefreshToken: (refreshToken: string): void => {
        const storage = getPersistStorage()
        storage.setItem(REFRESH_TOKEN_NAME_IN_STORAGE, refreshToken)
    },

    removeRefreshToken: (): void => {
        const storage = getPersistStorage()
        storage.removeItem(REFRESH_TOKEN_NAME_IN_STORAGE)
    },
}
